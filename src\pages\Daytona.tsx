import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { ScrollArea } from '@/components/ui/scroll-area.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Send, Bot, User, Code, Play, Terminal, Settings, AlertCircle, CheckCircle } from 'lucide-react'

function Daytona() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Ciao! Sono Manus AI. Posso aiutarti a generare e eseguire codice usando Daytona sandbox. Cosa vorresti creare oggi?',
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [daytonaStatus, setDaytonaStatus] = useState(daytonaService.getStatus())
  const [apiKey, setApiKey] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Aggiorna lo stato di Daytona periodicamente
    const interval = setInterval(() => {
      setDaytonaStatus(daytonaService.getStatus())
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    // Simula risposta dell'AI
    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 1,
        type: 'assistant',
        content: generateAIResponse(inputValue),
        timestamp: new Date(),
        hasCode: inputValue.toLowerCase().includes('codice') || inputValue.toLowerCase().includes('code'),
        codeLanguage: detectLanguage(inputValue),
        generatedCode: generateCode(inputValue)
      }
      setMessages(prev => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const generateAIResponse = (userInput) => {
    if (userInput.toLowerCase().includes('react')) {
      return 'Perfetto! Posso aiutarti a creare un componente React. Ecco un esempio di componente che potresti trovare utile:'
    } else if (userInput.toLowerCase().includes('python')) {
      return 'Ottima scelta! Python è perfetto per molte attività. Ecco un esempio di codice Python che posso eseguire nel sandbox Daytona:'
    } else if (userInput.toLowerCase().includes('api')) {
      return 'Le API sono fondamentali per le applicazioni moderne. Posso aiutarti a creare un\'API REST usando Flask o FastAPI:'
    } else if (userInput.toLowerCase().includes('dashboard')) {
      return 'Creerò un componente dashboard React moderno per te. Ecco il codice:'
    } else {
      return 'Interessante! Posso aiutarti con quello. Fammi sapere se hai bisogno di codice specifico e lo eseguirò nel sandbox Daytona per te.'
    }
  }

  const detectLanguage = (input) => {
    if (input.toLowerCase().includes('react') || input.toLowerCase().includes('jsx')) return 'javascript'
    if (input.toLowerCase().includes('python')) return 'python'
    if (input.toLowerCase().includes('typescript')) return 'typescript'
    return 'javascript'
  }

  const generateCode = (input) => {
    if (input.toLowerCase().includes('dashboard')) {
      return `function Dashboard() {
  const [data, setData] = useState([])
  
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold">Utenti Attivi</h2>
          <p className="text-3xl text-blue-600">1,234</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold">Vendite</h2>
          <p className="text-3xl text-green-600">€45,678</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold">Ordini</h2>
          <p className="text-3xl text-purple-600">567</p>
        </div>
      </div>
    </div>
  )
}`
    } else if (input.toLowerCase().includes('python')) {
      return `def hello_world():
    print("Hello from Daytona Sandbox!")
    return "Execution completed successfully"

result = hello_world()
print(f"Result: {result}")`
    } else {
      return `function HelloWorld() {
  return <div>Hello from Manus AI!</div>
}`
    }
  }

  const executeInSandbox = async (code, language = 'javascript') => {
    try {
      setIsLoading(true)
      
      // Esegue il codice in modalità demo
      const result = await daytonaService.executeCode(code, language)
      
      // Aggiunge il risultato alla chat
      const executionMessage = {
        id: Date.now(),
        type: 'assistant',
        content: result.isDemo 
          ? `Risultato (modalità demo): ${result.result}`
          : `Risultato esecuzione: ${result.result}`,
        timestamp: new Date(),
        isExecution: true,
        executionResult: result
      }
      
      setMessages(prev => [...prev, executionMessage])
      setDaytonaStatus(daytonaService.getStatus())
      
    } catch (error) {
      const errorMessage = {
        id: Date.now(),
        type: 'assistant',
        content: `Errore nell'esecuzione: ${error.message}`,
        timestamp: new Date(),
        isError: true
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const initializeDaytona = async () => {
    if (!apiKey.trim()) {
      alert('Inserisci una API key valida')
      return
    }
    
    const success = await daytonaService.initialize(apiKey)
    if (success) {
      setShowSettings(false)
      setDaytonaStatus(daytonaService.getStatus())
      
      const successMessage = {
        id: Date.now(),
        type: 'assistant',
        content: 'Daytona è stato configurato con successo! Ora posso eseguire codice reale nel sandbox.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, successMessage])
    } else {
      alert('Errore nella configurazione di Daytona. Verifica la API key.')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto max-w-4xl h-screen flex flex-col p-4">
        {/* Header */}
        <Card className="mb-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-800/80">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-xl font-bold">
              <Bot className="w-6 h-6 text-blue-600" />
              Manus AI Chat
              <span className="text-sm font-normal text-muted-foreground ml-auto flex items-center gap-2">
                con Daytona Sandbox
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowSettings(!showSettings)}
                  className="h-6 w-6 p-0"
                >
                  <Settings className="w-4 h-4" />
                </Button>
              </span>
            </CardTitle>
            
            {/* Settings Panel */}
            {showSettings && (
              <div className="mt-4 p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <h3 className="text-sm font-semibold mb-2">Configurazione Daytona</h3>
                <div className="flex gap-2">
                  <Input
                    type="password"
                    placeholder="Inserisci Daytona API Key"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={initializeDaytona} size="sm">
                    Configura
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Ottieni la tua API key dal <a href="https://www.daytona.io" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Daytona Dashboard</a>
                </p>
              </div>
            )}
          </CardHeader>
        </Card>

        {/* Chat Area */}
        <Card className="flex-1 shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-800/80 flex flex-col">
          <CardContent className="flex-1 p-0">
            <ScrollArea className="h-full p-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.type === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.type === 'assistant' && (
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mt-1">
                        <Bot className="w-4 h-4 text-white" />
                      </div>
                    )}
                    
                    <div
                      className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                        message.type === 'user'
                          ? 'bg-blue-600 text-white ml-auto'
                          : message.isError
                          ? 'bg-red-100 dark:bg-red-900 text-red-900 dark:text-red-100'
                          : 'bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-slate-100'
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                      
                      {message.hasCode && message.type === 'assistant' && (
                        <div className="mt-3 p-3 bg-slate-900 dark:bg-slate-800 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Code className="w-4 h-4 text-green-400" />
                              <span className="text-xs text-green-400 font-mono">
                                {message.codeLanguage || 'javascript'}
                              </span>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 px-2 text-xs text-green-400 hover:text-green-300"
                              onClick={() => executeInSandbox(message.generatedCode, message.codeLanguage)}
                              disabled={isLoading}
                            >
                              <Play className="w-3 h-3 mr-1" />
                              Esegui
                            </Button>
                          </div>
                          <pre className="text-xs text-green-300 font-mono overflow-x-auto">
                            <code>{message.generatedCode}</code>
                          </pre>
                        </div>
                      )}

                      {message.isExecution && (
                        <div className="mt-2 p-2 bg-slate-800 dark:bg-slate-900 rounded text-xs">
                          <div className="flex items-center gap-1 mb-1">
                            {message.executionResult.success ? (
                              <CheckCircle className="w-3 h-3 text-green-400" />
                            ) : (
                              <AlertCircle className="w-3 h-3 text-red-400" />
                            )}
                            <span className="text-slate-300">
                              Tempo: {Math.round(message.executionResult.executionTime)}ms
                            </span>
                            {message.executionResult.isDemo && (
                              <Badge variant="secondary" className="text-xs">Demo</Badge>
                            )}
                          </div>
                          {message.executionResult.output && (
                            <pre className="text-slate-300 font-mono">{message.executionResult.output}</pre>
                          )}
                        </div>
                      )}
                      
                      <div className="text-xs opacity-70 mt-2">
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>

                    {message.type === 'user' && (
                      <div className="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center flex-shrink-0 mt-1">
                        <User className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mt-1">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-slate-100 dark:bg-slate-700 rounded-2xl px-4 py-3">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div ref={messagesEndRef} />
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Input Area */}
        <Card className="mt-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-800/80">
          <CardContent className="p-4">
            <div className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Scrivi un messaggio... (premi Enter per inviare)"
                className="flex-1 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                disabled={isLoading}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Terminal className="w-3 h-3" />
                <span>Daytona Sandbox</span>
                <Badge variant="secondary" className="text-xs">Demo</Badge>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                <span>Modalità Demo</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Daytona