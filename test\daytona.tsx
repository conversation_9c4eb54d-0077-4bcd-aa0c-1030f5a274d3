import { Daytona, DaytonaConfig } from "@daytonaio/sdk";

// Initialize the SDK (uses environment variables by default)
const config: DaytonaConfig = {
  apiKey:
    "dtn_3f46e9eceed5cf17ba023fd4991fcb3eddede94ef88a4e5f2439c077250e1a50",
  target: "eu",
};

const daytona = new Daytona(config);

async function main() {
  // Create a new sandbox
 const sandbox = await daytona.create();
 const appCode = Buffer.from(`
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Hello World</title>
        <link rel="icon" href="https://www.daytona.io/favicon.ico">
    </head>
    <body style="display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #0a0a0a; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 2rem; border-radius: 10px; background-color: white; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <img src="https://raw.githubusercontent.com/daytonaio/daytona/main/assets/images/Daytona-logotype-black.png" alt="Daytona Logo" style="width: 180px; margin: 10px 0px;">
            <p>This web app is running in a Daytona sandbox!</p>
        </div>
    </body>
    </html>
    """

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000)
  `);


   await sandbox.fs.uploadFile(appCode, "app.py");


 // Create a new session and execute a command
  const execSessionId = "python-app-session";
  await sandbox.process.createSession(execSessionId);

  await sandbox.process.executeSessionCommand(execSessionId, ({
    command: `python app.py`,
    async: true,
  }));

  // Get the preview link for the Flask app
  const previewInfo = sandbox.getPreviewLink(3000);
  //console.log(`Flask app is available at: ${previewInfo.url}`);

  // Clean up
  await daytona.remove(sandbox);
}

main().catch(error => console.error("Error:", error));
